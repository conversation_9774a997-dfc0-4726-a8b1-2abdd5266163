#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大模型接入Neo4j的智能问答系统
结合传统知识图谱和现代大语言模型的优势
"""

import json
import re
from py2neo import Graph
from typing import List, Dict, Any, Optional

class LLMNeo4jChatbot:
    """
    大模型驱动的Neo4j知识图谱问答系统
    
    核心思路：
    1. 使用大模型理解用户意图和实体识别
    2. 智能生成Cypher查询语句
    3. 执行Neo4j查询获取结构化数据
    4. 使用大模型生成自然语言回答
    """
    
    def __init__(self, neo4j_auth=("neo4j", "password")):
        self.graph = Graph(auth=neo4j_auth)
        
        # 知识图谱schema信息
        self.schema_info = self._get_graph_schema()
        
        # 大模型提示词模板
        self.cypher_generation_prompt = self._build_cypher_prompt()
        self.answer_generation_prompt = self._build_answer_prompt()
    
    def _get_graph_schema(self) -> Dict[str, Any]:
        """
        获取Neo4j图谱的schema信息
        这是大模型生成准确Cypher查询的关键
        """
        schema = {
            "node_labels": [],
            "relationship_types": [],
            "properties": {},
            "sample_queries": []
        }
        
        try:
            # 获取节点标签
            labels_result = self.graph.run("CALL db.labels()").data()
            schema["node_labels"] = [item["label"] for item in labels_result]
            
            # 获取关系类型
            rels_result = self.graph.run("CALL db.relationshipTypes()").data()
            schema["relationship_types"] = [item["relationshipType"] for item in rels_result]
            
            # 获取属性信息
            props_result = self.graph.run("CALL db.propertyKeys()").data()
            schema["properties"] = [item["propertyKey"] for item in props_result]
            
            # 添加示例查询
            schema["sample_queries"] = [
                "MATCH (d:Disease)-[:has_symptom]->(s:Symptom) WHERE d.name = '感冒' RETURN s.name",
                "MATCH (d:Disease) WHERE d.name CONTAINS '糖尿病' RETURN d.cause",
                "MATCH (f:Food)-[:not_eat]->(d:Disease) WHERE d.name = '高血压' RETURN f.name"
            ]
            
        except Exception as e:
            print(f"获取schema失败: {e}")
        
        return schema
    
    def _build_cypher_prompt(self) -> str:
        """
        构建用于生成Cypher查询的提示词
        """
        return f"""
你是一个专业的Neo4j Cypher查询生成专家。

## 知识图谱Schema信息：
- 节点标签: {', '.join(self.schema_info.get('node_labels', []))}
- 关系类型: {', '.join(self.schema_info.get('relationship_types', []))}
- 属性字段: {', '.join(self.schema_info.get('properties', []))}

## 示例查询：
{chr(10).join(self.schema_info.get('sample_queries', []))}

## 任务：
根据用户的自然语言问题，生成准确的Cypher查询语句。

## 要求：
1. 只返回Cypher查询语句，不要其他解释
2. 使用LIMIT限制结果数量（通常10-20条）
3. 确保查询语法正确
4. 优先匹配最相关的节点和关系

用户问题：{{question}}

Cypher查询：
"""
    
    def _build_answer_prompt(self) -> str:
        """
        构建用于生成自然语言回答的提示词
        """
        return """
你是一个专业的医疗知识助手。

## 任务：
根据用户问题和从知识图谱查询到的结构化数据，生成准确、友好的自然语言回答。

## 要求：
1. 回答要准确、专业但易懂
2. 如果数据为空，给出合适的提示
3. 保持医疗建议的谨慎性，建议咨询专业医生
4. 回答要简洁明了，重点突出

用户问题：{question}

查询结果：{query_results}

请生成自然语言回答：
"""
    
    def extract_entities(self, question: str) -> Dict[str, List[str]]:
        """
        使用大模型进行实体识别和意图理解
        这里可以接入OpenAI、Claude等大模型API
        """
        # 简化版本：使用规则匹配（实际应用中替换为大模型调用）
        entities = {
            "diseases": [],
            "symptoms": [],
            "foods": [],
            "drugs": []
        }
        
        # 这里应该调用大模型API进行实体识别
        # entities = self.call_llm_for_entity_extraction(question)
        
        return entities
    
    def generate_cypher(self, question: str) -> str:
        """
        使用大模型生成Cypher查询
        """
        prompt = self.cypher_generation_prompt.format(question=question)
        
        # 这里应该调用大模型API
        # cypher = self.call_llm_api(prompt)
        
        # 简化版本：基于问题类型生成查询
        cypher = self._rule_based_cypher_generation(question)
        
        return cypher
    
    def _rule_based_cypher_generation(self, question: str) -> str:
        """
        基于规则的Cypher生成（作为大模型的fallback）
        """
        question_lower = question.lower()
        
        if "症状" in question and any(disease in question for disease in ["感冒", "发烧", "咳嗽"]):
            return """
            MATCH (d:Disease)-[:has_symptom]->(s:Symptom) 
            WHERE d.name CONTAINS '感冒' 
            RETURN d.name as disease, s.name as symptom 
            LIMIT 10
            """
        elif "不能吃" in question or "忌口" in question:
            return """
            MATCH (d:Disease)-[:not_eat]->(f:Food) 
            WHERE d.name CONTAINS '高血压' 
            RETURN d.name as disease, f.name as food 
            LIMIT 10
            """
        else:
            return """
            MATCH (n) 
            RETURN n.name as name 
            LIMIT 5
            """
    
    def execute_cypher(self, cypher: str) -> List[Dict[str, Any]]:
        """
        执行Cypher查询
        """
        try:
            result = self.graph.run(cypher).data()
            return result
        except Exception as e:
            print(f"Cypher执行失败: {e}")
            return []
    
    def generate_answer(self, question: str, query_results: List[Dict]) -> str:
        """
        使用大模型生成自然语言回答
        """
        prompt = self.answer_generation_prompt.format(
            question=question,
            query_results=json.dumps(query_results, ensure_ascii=False, indent=2)
        )
        
        # 这里应该调用大模型API
        # answer = self.call_llm_api(prompt)
        
        # 简化版本：基于结果生成回答
        answer = self._rule_based_answer_generation(question, query_results)
        
        return answer
    
    def _rule_based_answer_generation(self, question: str, results: List[Dict]) -> str:
        """
        基于规则的回答生成（作为大模型的fallback）
        """
        if not results:
            return "抱歉，我没有找到相关信息。建议您咨询专业医生。"
        
        if "症状" in question:
            symptoms = [item.get('symptom', '') for item in results if item.get('symptom')]
            if symptoms:
                return f"相关症状包括：{', '.join(symptoms[:5])}。如有疑问，请及时就医。"
        
        elif "不能吃" in question or "忌口" in question:
            foods = [item.get('food', '') for item in results if item.get('food')]
            if foods:
                return f"建议避免食用：{', '.join(foods[:5])}。具体饮食建议请咨询医生。"
        
        return f"查询到{len(results)}条相关信息，建议咨询专业医生获取详细建议。"
    
    def chat(self, question: str) -> str:
        """
        主要的对话接口
        """
        try:
            # 1. 实体识别和意图理解
            entities = self.extract_entities(question)
            
            # 2. 生成Cypher查询
            cypher = self.generate_cypher(question)
            print(f"生成的Cypher: {cypher}")
            
            # 3. 执行查询
            results = self.execute_cypher(cypher)
            print(f"查询结果: {results}")
            
            # 4. 生成自然语言回答
            answer = self.generate_answer(question, results)
            
            return answer
            
        except Exception as e:
            return f"处理问题时出现错误: {e}。请重新提问或咨询专业医生。"

# 使用示例
if __name__ == "__main__":
    # 创建聊天机器人实例
    chatbot = LLMNeo4jChatbot()
    
    # 测试对话
    test_questions = [
        "感冒有什么症状？",
        "高血压患者不能吃什么？",
        "糖尿病的原因是什么？"
    ]
    
    for question in test_questions:
        print(f"\n用户: {question}")
        answer = chatbot.chat(question)
        print(f"助手: {answer}")
