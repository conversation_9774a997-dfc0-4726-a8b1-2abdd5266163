# 虚拟环境设置说明

## 项目依赖

本项目已经设置了Python虚拟环境，包含以下主要依赖：

- **py2neo**: Neo4j图数据库Python驱动
- **pyahocorasick**: 高效字符串匹配算法
- **lxml**: XML/HTML解析库
- **pymongo**: MongoDB Python驱动
- **其他依赖**: 详见 `requirements.txt`

## 使用方法

### 1. 激活虚拟环境

```bash
# 方法1: 使用便捷脚本
source activate_env.sh

# 方法2: 直接激活
source venv/bin/activate
```

### 2. 验证环境

激活后，你应该看到命令行提示符前有 `(venv)` 标识。

### 3. 运行项目

```bash
# 激活虚拟环境后，可以运行项目文件
python build_medicalgraph.py
python chatbot_graph.py
```

### 4. 退出虚拟环境

```bash
deactivate
```

## 重新安装依赖

如果需要在其他环境中重新安装依赖：

```bash
# 创建新的虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

## 添加新依赖

如果需要添加新的依赖包：

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装新包
pip install 新包名

# 更新requirements.txt
pip freeze > requirements.txt
```

## 注意事项

1. 确保在运行项目前激活虚拟环境
2. 项目需要Neo4j数据库服务运行在本地7474端口
3. 如果使用MongoDB相关功能，需要确保MongoDB服务正在运行
4. 虚拟环境文件夹 `venv/` 已在 `.gitignore` 中（如果有的话）

## 故障排除

### 常见问题及解决方案

#### 1. py2neo 连接错误
**问题**: `ValueError: The following settings are not supported: {'http_port': 7474}`

**解决方案**: 已修复！新版本 py2neo (2021.2.4) 不再支持 `http_port` 参数。我们已经更新了连接方式：
```python
# 旧版本（已修复）
self.g = Graph(host="127.0.0.1", http_port=7474, user="neo4j", password="password")

# 新版本（当前使用）
self.g = Graph(auth=("neo4j", "password"))
```

#### 2. 导入错误
如果遇到导入错误：
1. 确认虚拟环境已激活
2. 检查 `requirements.txt` 中的包是否都已安装
3. 重新安装依赖：`pip install -r requirements.txt`

#### 3. Neo4j 连接问题
确保 Neo4j 数据库服务正在运行：
- 默认连接地址：`bolt://localhost:7687`
- 默认用户名：`neo4j`
- 默认密码：`password`（请根据实际情况修改）

#### 4. 数据文件问题
确保 `data/medical.json` 文件存在且格式正确。
