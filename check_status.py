#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目状态检查脚本
检查虚拟环境、依赖包、Neo4j连接等状态
"""

import sys
import os
import subprocess

def check_virtual_env():
    """检查虚拟环境状态"""
    print("=" * 50)
    print("检查虚拟环境状态")
    print("=" * 50)
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✓ 虚拟环境已激活")
        print(f"  Python路径: {sys.executable}")
        print(f"  虚拟环境路径: {sys.prefix}")
    else:
        print("✗ 虚拟环境未激活")
        print("  请运行: source venv/bin/activate")
        return False
    
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n" + "=" * 50)
    print("检查依赖包")
    print("=" * 50)
    
    required_packages = [
        'py2neo',
        'pyahocorasick', 
        'lxml',
        'pymongo'
    ]
    
    all_installed = True
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            all_installed = False
    
    return all_installed

def check_neo4j_connection():
    """检查Neo4j连接"""
    print("\n" + "=" * 50)
    print("检查Neo4j连接")
    print("=" * 50)
    
    try:
        from py2neo import Graph
        g = Graph(auth=("neo4j", "password"))
        # 尝试简单查询
        result = g.run("RETURN 1 as test").data()
        print("✓ Neo4j连接成功")
        print(f"  测试查询结果: {result}")
        return True
    except Exception as e:
        print("✗ Neo4j连接失败")
        print(f"  错误信息: {e}")
        print("  请确保Neo4j服务正在运行")
        return False

def check_data_files():
    """检查数据文件"""
    print("\n" + "=" * 50)
    print("检查数据文件")
    print("=" * 50)
    
    data_file = "data/medical.json"
    if os.path.exists(data_file):
        file_size = os.path.getsize(data_file)
        print(f"✓ {data_file} 存在")
        print(f"  文件大小: {file_size:,} 字节")
        
        # 检查文件内容
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                lines = sum(1 for _ in f)
            print(f"  数据条数: {lines:,} 条")
        except Exception as e:
            print(f"  读取文件时出错: {e}")
            return False
        
        return True
    else:
        print(f"✗ {data_file} 不存在")
        return False

def check_project_files():
    """检查项目文件"""
    print("\n" + "=" * 50)
    print("检查项目文件")
    print("=" * 50)
    
    required_files = [
        'build_medicalgraph.py',
        'answer_search.py',
        'question_classifier.py',
        'question_parser.py',
        'chatbot_graph.py'
    ]
    
    all_exist = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} 存在")
        else:
            print(f"✗ {file} 不存在")
            all_exist = False
    
    return all_exist

def test_imports():
    """测试模块导入"""
    print("\n" + "=" * 50)
    print("测试模块导入")
    print("=" * 50)
    
    modules = [
        'build_medicalgraph',
        'answer_search', 
        'question_classifier',
        'question_parser',
        'chatbot_graph'
    ]
    
    all_imported = True
    
    for module in modules:
        try:
            __import__(module)
            print(f"✓ {module} 导入成功")
        except Exception as e:
            print(f"✗ {module} 导入失败: {e}")
            all_imported = False
    
    return all_imported

def main():
    """主函数"""
    print("医疗知识图谱问答系统 - 状态检查")
    print("=" * 60)
    
    checks = [
        ("虚拟环境", check_virtual_env),
        ("依赖包", check_dependencies),
        ("Neo4j连接", check_neo4j_connection),
        ("数据文件", check_data_files),
        ("项目文件", check_project_files),
        ("模块导入", test_imports)
    ]
    
    results = {}
    
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print(f"\n检查 {name} 时出错: {e}")
            results[name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("检查结果总结")
    print("=" * 60)
    
    all_passed = True
    for name, passed in results.items():
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{name:12} : {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有检查都通过！项目可以正常运行。")
        print("\n运行项目:")
        print("  python build_medicalgraph.py  # 构建知识图谱")
        print("  python chatbot_graph.py       # 启动问答系统")
    else:
        print("⚠️  部分检查未通过，请根据上述信息进行修复。")
        print("\n常见解决方案:")
        print("  1. 激活虚拟环境: source venv/bin/activate")
        print("  2. 安装依赖: pip install -r requirements.txt")
        print("  3. 启动Neo4j服务")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
