#!/bin/bash
# 激活虚拟环境的便捷脚本
# 使用方法: source activate_env.sh

echo "正在激活虚拟环境..."
source venv/bin/activate

echo "虚拟环境已激活！"
echo "Python路径: $(which python)"
echo "Python版本: $(python --version)"
echo ""
echo "已安装的主要依赖包："
echo "- py2neo: $(python -c 'import py2neo; print(py2neo.__version__)')"
echo "- lxml: $(python -c 'import lxml; print(lxml.__version__)')"
echo "- pymongo: $(python -c 'import pymongo; print(pymongo.__version__)')"
echo "- pyahocorasick: 已安装"
echo ""
echo "要退出虚拟环境，请运行: deactivate"
